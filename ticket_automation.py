import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import logging

class TicketBot:
    def __init__(self, headless=False):
        self.setup_driver(headless)
        self.setup_logging()
    
    def setup_driver(self, headless):
        options = Options()
        if headless:
            options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        self.driver = webdriver.Chrome(options=options)
        self.wait = WebDriverWait(self.driver, 10)
    
    def setup_logging(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        self.logger = logging.getLogger(__name__)
    
    def login(self, username, password):
        """登录账户"""
        try:
            # 找到登录按钮并点击
            login_btn = self.wait.until(EC.element_to_be_clickable((By.CLASS_NAME, "login-btn")))
            login_btn.click()
            
            # 输入用户名和密码
            username_input = self.wait.until(EC.presence_of_element_located((By.ID, "username")))
            password_input = self.driver.find_element(By.ID, "password")
            
            username_input.send_keys(username)
            password_input.send_keys(password)
            
            # 点击登录
            submit_btn = self.driver.find_element(By.CLASS_NAME, "submit-btn")
            submit_btn.click()
            
            self.logger.info("登录成功")
            return True
        except Exception as e:
            self.logger.error(f"登录失败: {e}")
            return False
    
    def wait_for_sale_start(self, target_time):
        """等待开售时间"""
        while True:
            current_time = time.time()
            if current_time >= target_time:
                break
            time.sleep(0.1)
    
    def select_tickets(self, ticket_type, quantity):
        """选择票档和数量"""
        try:
            # 选择票档
            ticket_element = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//div[contains(text(), '{ticket_type}')]"))
            )
            ticket_element.click()
            
            # 选择数量
            for _ in range(quantity):
                plus_btn = self.driver.find_element(By.CLASS_NAME, "plus-btn")
                plus_btn.click()
                time.sleep(0.1)
            
            self.logger.info(f"已选择 {quantity} 张 {ticket_type} 票")
            return True
        except Exception as e:
            self.logger.error(f"选票失败: {e}")
            return False
    
    def submit_order(self):
        """提交订单"""
        try:
            # 点击立即购买
            buy_btn = self.wait.until(EC.element_to_be_clickable((By.CLASS_NAME, "buy-btn")))
            buy_btn.click()
            
            # 确认订单
            confirm_btn = self.wait.until(EC.element_to_be_clickable((By.CLASS_NAME, "confirm-btn")))
            confirm_btn.click()
            
            self.logger.info("订单提交成功")
            return True
        except Exception as e:
            self.logger.error(f"提交订单失败: {e}")
            return False
    
    def run(self, url, username, password, ticket_type, quantity, sale_time=None):
        """主运行函数"""
        try:
            self.driver.get(url)
            
            # 登录
            if not self.login(username, password):
                return False
            
            # 如果设置了开售时间，等待开售
            if sale_time:
                self.wait_for_sale_start(sale_time)
            
            # 快速刷新页面直到可以购买
            while True:
                try:
                    self.driver.refresh()
                    time.sleep(random.uniform(0.5, 1.0))
                    
                    # 检查是否可以购买
                    buy_btn = self.driver.find_element(By.CLASS_NAME, "buy-btn")
                    if buy_btn.is_enabled():
                        break
                except:
                    continue
            
            # 选择票档和数量
            if not self.select_tickets(ticket_type, quantity):
                return False
            
            # 提交订单
            return self.submit_order()
            
        except Exception as e:
            self.logger.error(f"运行失败: {e}")
            return False
        finally:
            self.driver.quit()

# 使用示例
if __name__ == "__main__":
    bot = TicketBot(headless=False)
    
    # 配置参数
    config = {
        'url': 'https://detail.damai.cn/item.htm?id=944170082832',  # 演出详情页
        'username': 'your_username',  # 请替换为您的大麦网用户名
        'password': 'your_password',  # 请替换为您的大麦网密码
        'ticket_type': '看台票',  # 票档名称，根据实际页面调整
        'quantity': 1,  # 购买数量
        'sale_time': None  # 开售时间戳，None表示立即开始
    }
    
    success = bot.run(**config)
    if success:
        print("抢票成功！")
    else:
        print("抢票失败！")