import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

class DamaiTicketBot:
    def __init__(self, headless=False):
        self.setup_driver(headless)
        self.setup_logging()
    
    def setup_driver(self, headless):
        options = Options()
        if headless:
            options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = webdriver.Chrome(options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, 10)
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO, 
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('damai_bot.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def login_if_needed(self):
        """检查是否需要登录，如果需要则等待手动登录"""
        try:
            # 检查是否已经登录
            self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "user-info")))
            self.logger.info("已经登录")
            return True
        except TimeoutException:
            try:
                # 查找登录按钮
                login_elements = self.driver.find_elements(By.XPATH, "//a[contains(text(), '登录') or contains(@class, 'login')]")
                if login_elements:
                    self.logger.info("检测到未登录状态，请手动登录...")
                    self.logger.info("请在浏览器中完成登录，然后按回车继续...")
                    input("登录完成后按回车继续...")
                    return True
            except:
                pass
        return True
    
    def wait_for_sale_start(self, target_time):
        """等待开售时间"""
        if target_time is None:
            return
        
        while True:
            current_time = time.time()
            remaining = target_time - current_time
            if remaining <= 0:
                self.logger.info("开售时间到！开始抢票...")
                break
            
            if remaining > 60:
                self.logger.info(f"距离开售还有 {int(remaining/60)} 分钟 {int(remaining%60)} 秒")
                time.sleep(30)
            else:
                self.logger.info(f"距离开售还有 {int(remaining)} 秒")
                time.sleep(1)
    
    def select_ticket_and_buy(self, ticket_type=None, quantity=1):
        """选择票档并购买"""
        try:
            # 等待页面加载完成
            time.sleep(2)
            
            # 查找票档选择区域
            ticket_selectors = [
                "//div[contains(@class, 'perform__order__price')]",
                "//div[contains(@class, 'price-list')]",
                "//div[contains(@class, 'ticket-price')]",
                "//li[contains(@class, 'price-item')]"
            ]
            
            ticket_found = False
            for selector in ticket_selectors:
                try:
                    tickets = self.driver.find_elements(By.XPATH, selector)
                    if tickets:
                        # 如果指定了票档类型，尝试找到匹配的
                        if ticket_type:
                            for ticket in tickets:
                                if ticket_type in ticket.text:
                                    ticket.click()
                                    ticket_found = True
                                    self.logger.info(f"选择了票档: {ticket.text}")
                                    break
                        else:
                            # 选择第一个可用的票档
                            tickets[0].click()
                            ticket_found = True
                            self.logger.info(f"选择了票档: {tickets[0].text}")
                        
                        if ticket_found:
                            break
                except Exception as e:
                    continue
            
            if not ticket_found:
                self.logger.warning("未找到票档选择器，尝试直接购买")
            
            time.sleep(1)
            
            # 设置购买数量
            self.set_ticket_quantity(quantity)
            
            # 查找并点击购买按钮
            buy_selectors = [
                "//button[contains(text(), '立即购买')]",
                "//button[contains(text(), '立即预订')]",
                "//a[contains(text(), '立即购买')]",
                "//a[contains(text(), '立即预订')]",
                "//div[contains(@class, 'buy-btn')]",
                "//button[contains(@class, 'buy')]"
            ]
            
            for selector in buy_selectors:
                try:
                    buy_btn = self.driver.find_element(By.XPATH, selector)
                    if buy_btn.is_enabled():
                        buy_btn.click()
                        self.logger.info("点击购买按钮成功")
                        return True
                except:
                    continue
            
            self.logger.error("未找到可用的购买按钮")
            return False
            
        except Exception as e:
            self.logger.error(f"选票购买失败: {e}")
            return False
    
    def set_ticket_quantity(self, quantity):
        """设置票数量"""
        try:
            # 查找数量选择器
            quantity_selectors = [
                "//input[contains(@class, 'quantity')]",
                "//select[contains(@class, 'quantity')]",
                "//div[contains(@class, 'quantity-selector')]"
            ]
            
            for selector in quantity_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.tag_name == 'input':
                        element.clear()
                        element.send_keys(str(quantity))
                    elif element.tag_name == 'select':
                        from selenium.webdriver.support.ui import Select
                        select = Select(element)
                        select.select_by_value(str(quantity))
                    
                    self.logger.info(f"设置购买数量为: {quantity}")
                    return True
                except:
                    continue
            
            # 如果没找到数量选择器，尝试点击加号按钮
            for i in range(quantity - 1):
                try:
                    plus_btn = self.driver.find_element(By.XPATH, "//button[contains(@class, 'plus') or text()='+']")
                    plus_btn.click()
                    time.sleep(0.2)
                except:
                    break
                    
        except Exception as e:
            self.logger.warning(f"设置数量失败: {e}")
    
    def run(self, url, ticket_type=None, quantity=1, sale_time=None, max_attempts=100):
        """主运行函数"""
        try:
            self.logger.info(f"开始访问页面: {url}")
            self.driver.get(url)
            
            # 检查登录状态
            self.login_if_needed()
            
            # 等待开售时间
            self.wait_for_sale_start(sale_time)
            
            # 开始抢票循环
            for attempt in range(max_attempts):
                try:
                    self.logger.info(f"第 {attempt + 1} 次尝试抢票...")
                    
                    # 刷新页面
                    self.driver.refresh()
                    time.sleep(random.uniform(0.5, 1.5))
                    
                    # 尝试选票并购买
                    if self.select_ticket_and_buy(ticket_type, quantity):
                        self.logger.info("抢票成功！请在浏览器中完成后续支付流程")
                        return True
                    
                    # 随机等待避免被检测
                    time.sleep(random.uniform(1, 3))
                    
                except Exception as e:
                    self.logger.error(f"第 {attempt + 1} 次尝试失败: {e}")
                    time.sleep(random.uniform(0.5, 2))
            
            self.logger.error(f"尝试 {max_attempts} 次后仍未成功")
            return False
            
        except Exception as e:
            self.logger.error(f"运行失败: {e}")
            return False
        finally:
            # 不自动关闭浏览器，让用户手动完成后续操作
            self.logger.info("脚本执行完成，浏览器保持打开状态以便手动操作")

# 使用示例
if __name__ == "__main__":
    bot = DamaiTicketBot(headless=False)
    
    # 配置参数
    config = {
        'url': 'https://detail.damai.cn/item.htm?id=944170082832',
        'ticket_type': None,  # 票档名称，None表示选择第一个可用的
        'quantity': 1,  # 购买数量
        'sale_time': None,  # 开售时间戳，None表示立即开始
        'max_attempts': 50  # 最大尝试次数
    }
    
    print("=== 大麦网抢票脚本 ===")
    print(f"目标URL: {config['url']}")
    print(f"购买数量: {config['quantity']}")
    print("注意：请确保已经登录大麦网账户")
    print("=" * 50)
    
    success = bot.run(**config)
    if success:
        print("抢票成功！请完成支付流程")
    else:
        print("抢票失败，请检查日志")
