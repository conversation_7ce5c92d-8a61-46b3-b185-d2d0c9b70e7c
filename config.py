# 大麦网抢票配置文件
# 南征北战「NEW LIFE」2025巡演 成都站

# 目标演出页面URL
TARGET_URL = "https://detail.damai.cn/item.htm?id=944170082832"

# 票档设置
TICKET_CONFIG = {
    'ticket_type': '全价票199元',  # 可选: '普通票159元', '全价票199元', 'VIP299元', 'SVIP499元'
    'quantity': 1,                # 购买数量 (每笔订单限购6张)
}

# 抢票设置
BOT_CONFIG = {
    'headless': False,     # 是否无头模式运行（True=后台运行，False=显示浏览器）
    'max_attempts': 100,   # 最大尝试次数
    'sale_time': None,     # 开售时间戳，None表示立即开始
}

# 时间设置示例（如果知道具体开售时间）
# import time
# from datetime import datetime
# 
# # 设置开售时间 - 例如：2024年1月1日 10:00:00
# sale_datetime = datetime(2024, 1, 1, 10, 0, 0)
# BOT_CONFIG['sale_time'] = sale_datetime.timestamp()

# 南征北战成都站可选票档
AVAILABLE_TICKET_TYPES = [
    "普通票159元",    # 159元 (可能缺货)
    "全价票199元",    # 199元 (推荐)
    "VIP299元",       # 299元
    "SVIP499元"       # 499元
]

# 常见票档名称参考（根据实际页面调整）
COMMON_TICKET_TYPES = [
    "普通票159元",
    "全价票199元",
    "VIP299元",
    "SVIP499元",
    "内场票",
    "看台票"
]

# 使用说明
USAGE_INSTRUCTIONS = """
使用说明：
1. 确保已安装Chrome浏览器和ChromeDriver
2. 修改config.py中的配置参数
3. 运行 python damai_ticket_bot.py
4. 脚本会自动打开浏览器，请手动登录大麦网账户
5. 登录完成后按回车继续，脚本将开始自动抢票
6. 抢票成功后请手动完成支付流程

注意事项：
- 请遵守大麦网的使用条款
- 建议在开售前几分钟启动脚本
- 网络状况会影响抢票成功率
- 脚本仅用于学习和研究目的
"""
