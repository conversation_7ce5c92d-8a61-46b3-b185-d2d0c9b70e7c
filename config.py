# 大麦网抢票配置文件

# 目标演出页面URL
TARGET_URL = "https://detail.damai.cn/item.htm?spm=a2oeg.home.card_0.ditem_6.591b23e1NDGLZL&id=955900252094"

# 票档设置
TICKET_CONFIG = {
    'ticket_type': None,  # 票档名称，例如: "看台票", "内场票", None表示选择第一个可用的
    'quantity': 1,        # 购买数量
}

# 抢票设置
BOT_CONFIG = {
    'headless': False,     # 是否无头模式运行（True=后台运行，False=显示浏览器）
    'max_attempts': 100,   # 最大尝试次数
    'sale_time': None,     # 开售时间戳，None表示立即开始
}

# 时间设置示例（如果知道具体开售时间）
# import time
# from datetime import datetime
# 
# # 设置开售时间 - 例如：2024年1月1日 10:00:00
# sale_datetime = datetime(2024, 1, 1, 10, 0, 0)
# BOT_CONFIG['sale_time'] = sale_datetime.timestamp()

# 常见票档名称参考（根据实际页面调整）
COMMON_TICKET_TYPES = [
    "内场票",
    "看台票", 
    "VIP票",
    "普通票",
    "学生票",
    "早鸟票"
]

# 使用说明
USAGE_INSTRUCTIONS = """
使用说明：
1. 确保已安装Chrome浏览器和ChromeDriver
2. 修改config.py中的配置参数
3. 运行 python damai_ticket_bot.py
4. 脚本会自动打开浏览器，请手动登录大麦网账户
5. 登录完成后按回车继续，脚本将开始自动抢票
6. 抢票成功后请手动完成支付流程

注意事项：
- 请遵守大麦网的使用条款
- 建议在开售前几分钟启动脚本
- 网络状况会影响抢票成功率
- 脚本仅用于学习和研究目的
"""
