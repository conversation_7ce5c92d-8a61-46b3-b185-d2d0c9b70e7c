#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
大麦网抢票脚本启动器
"""

from damai_ticket_bot import DamaiTicketBot
from config import TARGET_URL, TICKET_CONFIG, BOT_CONFIG, USAGE_INSTRUCTIONS
import time

def main():
    print("=" * 60)
    print("           大麦网自动抢票脚本")
    print("=" * 60)
    print(USAGE_INSTRUCTIONS)
    print("=" * 60)
    
    # 显示当前配置
    print("当前配置:")
    print(f"目标URL: {TARGET_URL}")
    print(f"票档类型: {TICKET_CONFIG['ticket_type'] or '自动选择第一个可用'}")
    print(f"购买数量: {TICKET_CONFIG['quantity']}")
    print(f"最大尝试次数: {BOT_CONFIG['max_attempts']}")
    print(f"开售时间: {BOT_CONFIG['sale_time'] or '立即开始'}")
    print("=" * 60)
    
    # 确认开始
    confirm = input("确认配置无误，开始抢票？(y/n): ").lower().strip()
    if confirm != 'y':
        print("已取消")
        return
    
    # 创建机器人实例
    bot = DamaiTicketBot(headless=BOT_CONFIG['headless'])
    
    try:
        # 开始抢票
        success = bot.run(
            url=TARGET_URL,
            ticket_type=TICKET_CONFIG['ticket_type'],
            quantity=TICKET_CONFIG['quantity'],
            sale_time=BOT_CONFIG['sale_time'],
            max_attempts=BOT_CONFIG['max_attempts']
        )
        
        if success:
            print("\n🎉 抢票成功！请在浏览器中完成支付流程")
        else:
            print("\n❌ 抢票失败，请检查日志文件 damai_bot.log")
            
    except KeyboardInterrupt:
        print("\n用户中断了抢票过程")
    except Exception as e:
        print(f"\n发生错误: {e}")
    
    print("\n脚本执行完成")

if __name__ == "__main__":
    main()
